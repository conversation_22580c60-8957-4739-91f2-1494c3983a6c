{
  "name": "WhatsApp Drive Assistant",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "whatsapp",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-whatsapp",
      "name": "WhatsApp Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [
        240,
        300
      ],
      "webhookId": "whatsapp-drive-webhook"
    },
    {
      "parameters": {
        "values": {
          "string": [
            {
              "name": "from",
              "value": "={{ $json.From }}"
            },
            {
              "name": "body",
              "value": "={{ $json.Body }}"
            },
            {
              "name": "messageSid",
              "value": "={{ $json.MessageSid }}"
            },
            {
              "name": "timestamp",
              "value": "={{ new Date().toISOString() }}"
            }
          ]
        },
        "options": {}
      },
      "id": "extract-message-data",
      "name": "Extract Message Data",
      "type": "n8n-nodes-base.set",
      "typeVersion": 1,
      "position": [
        460,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "// Parse WhatsApp command\nconst body = $input.item(0).json.body.trim().toUpperCase();\nconst from = $input.item(0).json.from;\nconst messageSid = $input.item(0).json.messageSid;\nconst timestamp = $input.item(0).json.timestamp;\n\n// Extract command and parameters\nlet command = '';\nlet path = '';\nlet targetPath = '';\nlet isValid = false;\nlet errorMessage = '';\n\n// Parse different command formats\nif (body.startsWith('LIST ')) {\n  command = 'LIST';\n  path = body.substring(5).trim();\n  isValid = path.length > 0;\n  if (!isValid) errorMessage = 'Please specify a folder path. Example: LIST /Projects';\n} else if (body.startsWith('DELETE ')) {\n  command = 'DELETE';\n  path = body.substring(7).trim();\n  isValid = path.length > 0;\n  if (!isValid) errorMessage = 'Please specify a file path. Example: DELETE /folder/file.pdf';\n} else if (body.startsWith('MOVE ')) {\n  command = 'MOVE';\n  const parts = body.substring(5).trim().split(' ');\n  if (parts.length >= 2) {\n    path = parts[0];\n    targetPath = parts.slice(1).join(' ');\n    isValid = path.length > 0 && targetPath.length > 0;\n  }\n  if (!isValid) errorMessage = 'Please specify source and target paths. Example: MOVE /source/file.pdf /target/folder';\n} else if (body.startsWith('SUMMARY ')) {\n  command = 'SUMMARY';\n  path = body.substring(8).trim();\n  isValid = path.length > 0;\n  if (!isValid) errorMessage = 'Please specify a folder path. Example: SUMMARY /Documents';\n} else if (body === 'HELP') {\n  command = 'HELP';\n  isValid = true;\n} else if (body.startsWith('CONFIRM DELETE')) {\n  command = 'CONFIRM_DELETE';\n  isValid = true;\n} else {\n  errorMessage = 'Unknown command. Send HELP for available commands.';\n}\n\nreturn {\n  command,\n  path,\n  targetPath,\n  from,\n  messageSid,\n  timestamp,\n  isValid,\n  errorMessage,\n  originalMessage: $input.item(0).json.body\n};"
      },
      "id": "parse-command",
      "name": "Parse Command",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        680,
        300
      ]
    },
    {
      "parameters": {
        "conditions": {
          "string": [
            {
              "value1": "={{ $json.isValid }}",
              "operation": "equal",
              "value2": "true"
            }
          ]
        }
      },
      "id": "check-valid-command",
      "name": "Valid Command?",
      "type": "n8n-nodes-base.if",
      "typeVersion": 1,
      "position": [
        900,
        300
      ]
    },
    {
      "parameters": {
        "rules": {
          "rules": [
            {
              "conditions": {
                "string": [
                  {
                    "value1": "={{ $json.command }}",
                    "operation": "equal",
                    "value2": "LIST"
                  }
                ]
              }
            },
            {
              "conditions": {
                "string": [
                  {
                    "value1": "={{ $json.command }}",
                    "operation": "equal",
                    "value2": "DELETE"
                  }
                ]
              }
            },
            {
              "conditions": {
                "string": [
                  {
                    "value1": "={{ $json.command }}",
                    "operation": "equal",
                    "value2": "MOVE"
                  }
                ]
              }
            },
            {
              "conditions": {
                "string": [
                  {
                    "value1": "={{ $json.command }}",
                    "operation": "equal",
                    "value2": "HELP"
                  }
                ]
              }
            },
            {
              "conditions": {
                "string": [
                  {
                    "value1": "={{ $json.command }}",
                    "operation": "equal",
                    "value2": "SUMMARY"
                  }
                ]
              }
            }
          ]
        }
      },
      "id": "switch-command",
      "name": "Command Router",
      "type": "n8n-nodes-base.switch",
      "typeVersion": 1,
      "position": [
        1120,
        200
      ]
    },
    {
      "parameters": {
        "resource": "file",
        "operation": "list",
        "folderId": "={{ $json.path.replace(/^\//, '') }}",
        "options": {
          "fields": "files(id,name,mimeType,size,modifiedTime)"
        }
      },
      "id": "list-files",
      "name": "List Files",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 3,
      "position": [
        1340,
        100
      ],
      "credentials": {
        "googleDriveOAuth2Api": {
          "id": "google-drive-oauth2",
          "name": "Google Drive OAuth2"
        }
      }
    },
    {
      "parameters": {
        "resource": "file",
        "operation": "delete",
        "fileId": "={{ $json.path }}"
      },
      "id": "delete-file",
      "name": "Delete File",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 3,
      "position": [
        1340,
        200
      ],
      "credentials": {
        "googleDriveOAuth2Api": {
          "id": "google-drive-oauth2",
          "name": "Google Drive OAuth2"
        }
      }
    },
    {
      "parameters": {
        "resource": "file",
        "operation": "move",
        "fileId": "={{ $json.path }}",
        "folderId": "={{ $json.targetPath }}"
      },
      "id": "move-file",
      "name": "Move File",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 3,
      "position": [
        1340,
        300
      ],
      "credentials": {
        "googleDriveOAuth2Api": {
          "id": "google-drive-oauth2",
          "name": "Google Drive OAuth2"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// Format file list response\nconst files = $input.all();\nlet response = '';\n\nif (files.length === 0) {\n  response = '📁 No files found in the specified folder.';\n} else {\n  response = `📁 Files in ${$json.path}:\\n\\n`;\n  \n  files.forEach(file => {\n    const name = file.json.name;\n    const size = file.json.size ? `(${Math.round(file.json.size / 1024)}KB)` : '';\n    const type = file.json.mimeType.includes('folder') ? '📁' : '📄';\n    response += `${type} ${name} ${size}\\n`;\n  });\n}\n\nreturn {\n  response,\n  from: $('Parse Command').item(0).json.from,\n  command: 'LIST'\n};"
      },
      "id": "format-list-response",
      "name": "Format List Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1560,
        100
      ]
    },
    {
      "parameters": {
        "jsCode": "// Format delete confirmation response\nconst fileName = $json.path.split('/').pop();\n\nreturn {\n  response: `✅ File '${fileName}' has been deleted successfully.`,\n  from: $('Parse Command').item(0).json.from,\n  command: 'DELETE'\n};"
      },
      "id": "format-delete-response",
      "name": "Format Delete Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1560,
        200
      ]
    },
    {
      "parameters": {
        "jsCode": "// Format move confirmation response\nconst fileName = $json.path.split('/').pop();\nconst targetFolder = $json.targetPath;\n\nreturn {\n  response: `✅ File '${fileName}' has been moved to ${targetFolder} successfully.`,\n  from: $('Parse Command').item(0).json.from,\n  command: 'MOVE'\n};"
      },
      "id": "format-move-response",
      "name": "Format Move Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1560,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "// Generate help response\nconst helpText = `🤖 WhatsApp Drive Assistant Commands:\\n\\n` +\n  `📋 LIST /folder/path - List files in folder\\n` +\n  `🗑️ DELETE /path/to/file - Delete a file\\n` +\n  `📁 MOVE /source/file /target/folder - Move file\\n` +\n  `📄 SUMMARY /folder/path - Get AI summaries\\n` +\n  `❓ HELP - Show this help\\n\\n` +\n  `Example: LIST /Projects/2024`;\n\nreturn {\n  response: helpText,\n  from: $('Parse Command').item(0).json.from,\n  command: 'HELP'\n};"
      },
      "id": "generate-help",
      "name": "Generate Help",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1340,
        400
      ]
    },
    {
      "parameters": {
        "jsCode": "// Generate error response\nconst errorMessage = $('Parse Command').item(0).json.errorMessage;\n\nreturn {\n  response: `❌ Error: ${errorMessage}\\n\\nSend HELP for available commands.`,\n  from: $('Parse Command').item(0).json.from,\n  command: 'ERROR'\n};"
      },
      "id": "send-error-response",
      "name": "Send Error Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        1120,
        400
      ]
    },
    {
      "parameters": {
        "resource": "file",
        "operation": "list",
        "folderId": "={{ $json.path.replace(/^\//, '') }}",
        "options": {
          "fields": "files(id,name,mimeType,size,modifiedTime)",
          "q": "mimeType='application/pdf' or mimeType='application/vnd.openxmlformats-officedocument.wordprocessingml.document' or mimeType='text/plain'"
        }
      },
      "id": "list-documents",
      "name": "List Documents",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 3,
      "position": [
        1340,
        500
      ],
      "credentials": {
        "googleDriveOAuth2Api": {
          "id": "google-drive-oauth2",
          "name": "Google Drive OAuth2"
        }
      }
    },
    {
      "parameters": {
        "resource": "file",
        "operation": "download",
        "fileId": "={{ $json.id }}",
        "options": {}
      },
      "id": "download-document",
      "name": "Download Document",
      "type": "n8n-nodes-base.googleDrive",
      "typeVersion": 3,
      "position": [
        1560,
        500
      ],
      "credentials": {
        "googleDriveOAuth2Api": {
          "id": "google-drive-oauth2",
          "name": "Google Drive OAuth2"
        }
      }
    },
    {
      "parameters": {
        "options": {}
      },
      "id": "extract-text",
      "name": "Extract Text",
      "type": "n8n-nodes-base.extractFromFile",
      "typeVersion": 1,
      "position": [
        1780,
        500
      ]
    },
    {
      "parameters": {
        "resource": "text",
        "operation": "message",
        "model": "gpt-4o",
        "messages": {
          "messages": [
            {
              "role": "system",
              "content": "You are a helpful assistant that creates concise bullet-point summaries of documents. Focus on key points, main ideas, and important details. Keep summaries brief but informative."
            },
            {
              "role": "user",
              "content": "Please summarize this document in bullet points:\\n\\n{{ $json.text }}"
            }
          ]
        },
        "options": {
          "maxTokens": 500,
          "temperature": 0.3
        }
      },
      "id": "summarize-with-ai",
      "name": "Summarize with AI",
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        2000,
        500
      ],
      "credentials": {
        "openAiApi": {
          "id": "openai-credentials",
          "name": "OpenAI API"
        }
      }
    },
    {
      "parameters": {
        "jsCode": "// Collect all summaries and format response\nconst summaries = $input.all();\nconst folderPath = $('Parse Command').item(0).json.path;\n\nlet response = `📄 Document Summaries for ${folderPath}:\\n\\n`;\n\nif (summaries.length === 0) {\n  response = '📁 No documents found in the specified folder.';\n} else {\n  summaries.forEach((item, index) => {\n    const fileName = item.json.name || `Document ${index + 1}`;\n    const summary = item.json.choices[0].message.content;\n    response += `📋 ${fileName}:\\n${summary}\\n\\n`;\n  });\n}\n\nreturn {\n  response,\n  from: $('Parse Command').item(0).json.from,\n  command: 'SUMMARY'\n};"
      },
      "id": "format-summary-response",
      "name": "Format Summary Response",
      "type": "n8n-nodes-base.code",
      "typeVersion": 1,
      "position": [
        2220,
        500
      ]
    }
    {
      "parameters": {
        "url": "https://api.twilio.com/2010-04-01/Accounts/{{ $env.TWILIO_ACCOUNT_SID }}/Messages.json",
        "authentication": "genericCredentialType",
        "genericAuthType": "httpBasicAuth",
        "httpBasicAuth": "twilio-credentials",
        "sendBody": true,
        "bodyContentType": "form-urlencoded",
        "bodyParameters": {
          "parameters": [
            {
              "name": "From",
              "value": "{{ $env.TWILIO_WHATSAPP_NUMBER }}"
            },
            {
              "name": "To",
              "value": "={{ $json.from }}"
            },
            {
              "name": "Body",
              "value": "={{ $json.response }}"
            }
          ]
        },
        "options": {}
      },
      "id": "send-whatsapp-response",
      "name": "Send WhatsApp Response",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4,
      "position": [
        1780,
        300
      ],
      "credentials": {
        "httpBasicAuth": {
          "id": "twilio-credentials",
          "name": "Twilio Credentials"
        }
      }
    },
    {
      "parameters": {
        "respondWith": "text",
        "responseBody": "OK"
      },
      "id": "webhook-response",
      "name": "Webhook Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [
        2000,
        300
      ]
    },
    {
      "parameters": {
        "values": {
          "string": [
            {
              "name": "timestamp",
              "value": "={{ new Date().toISOString() }}"
            },
            {
              "name": "from",
              "value": "={{ $json.from }}"
            },
            {
              "name": "command",
              "value": "={{ $json.command }}"
            },
            {
              "name": "path",
              "value": "={{ $json.path || 'N/A' }}"
            },
            {
              "name": "response",
              "value": "={{ $json.response }}"
            },
            {
              "name": "status",
              "value": "success"
            }
          ]
        }
      },
      "id": "audit-log",
      "name": "Audit Log",
      "type": "n8n-nodes-base.set",
      "typeVersion": 1,
      "position": [
        1780,
        500
      ]
    }
  ],
  "connections": {
    "WhatsApp Webhook": {
      "main": [
        [
          {
            "node": "Extract Message Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Extract Message Data": {
      "main": [
        [
          {
            "node": "Parse Command",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Parse Command": {
      "main": [
        [
          {
            "node": "Valid Command?",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Valid Command?": {
      "main": [
        [
          {
            "node": "Command Router",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Send Error Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Command Router": {
      "main": [
        [
          {
            "node": "List Files",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Delete File",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Move File",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Generate Help",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "List Files": {
      "main": [
        [
          {
            "node": "Format List Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Delete File": {
      "main": [
        [
          {
            "node": "Format Delete Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Move File": {
      "main": [
        [
          {
            "node": "Format Move Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Format List Response": {
      "main": [
        [
          {
            "node": "Send WhatsApp Response",
            "type": "main",
            "index": 0
          },
          {
            "node": "Audit Log",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Format Delete Response": {
      "main": [
        [
          {
            "node": "Send WhatsApp Response",
            "type": "main",
            "index": 0
          },
          {
            "node": "Audit Log",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Format Move Response": {
      "main": [
        [
          {
            "node": "Send WhatsApp Response",
            "type": "main",
            "index": 0
          },
          {
            "node": "Audit Log",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Help": {
      "main": [
        [
          {
            "node": "Send WhatsApp Response",
            "type": "main",
            "index": 0
          },
          {
            "node": "Audit Log",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Send Error Response": {
      "main": [
        [
          {
            "node": "Send WhatsApp Response",
            "type": "main",
            "index": 0
          },
          {
            "node": "Audit Log",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Send WhatsApp Response": {
      "main": [
        [
          {
            "node": "Webhook Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": false,
  "settings": {},
  "versionId": "1",
  "id": "whatsapp-drive-assistant",
  "meta": {
    "instanceId": "whatsapp-drive-assistant"
  },
  "tags": []
}
