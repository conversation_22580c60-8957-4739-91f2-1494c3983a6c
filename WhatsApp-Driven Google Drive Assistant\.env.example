# Twilio Configuration
# Get these from your Twilio Console (https://console.twilio.com/)
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# Google Drive Configuration
# Create OAuth2 credentials in Google Cloud Console
# Enable Google Drive API for your project
GOOGLE_CLIENT_ID=your_google_oauth2_client_id_here
GOOGLE_CLIENT_SECRET=your_google_oauth2_client_secret_here

# OpenAI Configuration
# Get your API key from https://platform.openai.com/
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o

# n8n Configuration
N8N_HOST=localhost
N8N_PORT=5678
N8N_PROTOCOL=http
N8N_BASIC_AUTH_ACTIVE=true
N8N_BASIC_AUTH_USER=admin
N8N_BASIC_AUTH_PASSWORD=your_secure_password_here

# Database Configuration (for n8n)
DB_TYPE=sqlite
DB_SQLITE_DATABASE=database.sqlite

# Security Configuration
N8N_ENCRYPTION_KEY=your_encryption_key_here

# Webhook Configuration
WEBHOOK_URL=https://your-n8n-instance.com/webhook/whatsapp

# Logging Configuration
LOG_LEVEL=info
ENABLE_AUDIT_LOG=true

# Safety Configuration
REQUIRE_DELETE_CONFIRMATION=true
MAX_FILES_PER_OPERATION=50

# AI Configuration
MAX_DOCUMENT_SIZE_MB=10
SUPPORTED_DOCUMENT_TYPES=pdf,docx,txt,doc

# Rate Limiting
MAX_REQUESTS_PER_MINUTE=10
MAX_REQUESTS_PER_HOUR=100
