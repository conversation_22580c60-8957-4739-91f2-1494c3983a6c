version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    container_name: whatsapp-drive-assistant
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_HOST=${N8N_HOST:-localhost}
      - N8N_PORT=${N8N_PORT:-5678}
      - N8N_PROTOCOL=${N8N_PROTOCOL:-http}
      - N8N_BASIC_AUTH_ACTIVE=${N8N_BASIC_AUTH_ACTIVE:-true}
      - N8N_BASIC_AUTH_USER=${N8N_BASIC_AUTH_USER:-admin}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_BASIC_AUTH_PASSWORD}
      - DB_TYPE=${DB_TYPE:-sqlite}
      - DB_SQLITE_DATABASE=${DB_SQLITE_DATABASE:-/home/<USER>/.n8n/database.sqlite}
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYP<PERSON><PERSON>_KEY}
      - WEBHOOK_URL=${WEBHO<PERSON>_URL}
      - GENERIC_TIMEZONE=UTC
      - TZ=UTC
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./workflows:/home/<USER>/.n8n/workflows
      - ./credentials:/home/<USER>/.n8n/credentials
    env_file:
      - .env
    networks:
      - n8n_network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: whatsapp-drive-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - n8n_network
    command: redis-server --appendonly yes

  # Optional: PostgreSQL for production database
  postgres:
    image: postgres:15-alpine
    container_name: whatsapp-drive-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=n8n
      - POSTGRES_USER=n8n
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-n8n_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - n8n_network
    ports:
      - "5432:5432"

volumes:
  n8n_data:
    driver: local
  redis_data:
    driver: local
  postgres_data:
    driver: local

networks:
  n8n_network:
    driver: bridge
