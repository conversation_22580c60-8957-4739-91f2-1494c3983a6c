{"name": "whatsapp-drive-assistant", "version": "1.0.0", "description": "WhatsApp-Driven Google Drive Assistant using n8n workflow", "main": "index.js", "scripts": {"start": "n8n start", "dev": "n8n start --tunnel", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "setup": "node scripts/setup.js", "test": "jest", "lint": "eslint .", "format": "prettier --write ."}, "keywords": ["n8n", "whatsapp", "google-drive", "automation", "workflow", "twi<PERSON>", "ai", "document-processing"], "author": "Your Name", "license": "MIT", "dependencies": {"n8n": "^1.0.0", "dotenv": "^16.3.1"}, "devDependencies": {"jest": "^29.7.0", "eslint": "^8.50.0", "prettier": "^3.0.3", "@types/node": "^20.8.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/whatsapp-drive-assistant.git"}, "bugs": {"url": "https://github.com/yourusername/whatsapp-drive-assistant/issues"}, "homepage": "https://github.com/yourusername/whatsapp-drive-assistant#readme"}