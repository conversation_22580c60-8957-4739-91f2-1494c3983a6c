# WhatsApp-Driven Google Drive Assistant

A powerful n8n workflow that enables Google Drive management through WhatsApp messages using Twilio integration.

## Features

- 📱 **WhatsApp Integration**: Send commands via WhatsApp using Twilio Sandbox
- 📁 **Google Drive Operations**: List, move, delete files and folders
- 🤖 **AI Summarization**: Get intelligent summaries of documents using OpenAI GPT-4
- 🔐 **Secure Authentication**: OAuth2 integration with Google Drive API
- 📊 **Audit Logging**: Complete audit trail of all operations
- 🛡️ **Safety Guards**: Protection against accidental mass deletions

## Supported Commands

Send these commands via WhatsApp:

```
LIST /folder/path          - List all files in the specified folder
DELETE /path/to/file.pdf   - Delete a specific file (requires confirmation)
MOVE /source/file.pdf /destination/folder - Move file to new location
SUMMARY /folder/path       - Get AI-powered summaries of all documents in folder
HELP                       - Show available commands
```

## Prerequisites

Before setting up the workflow, ensure you have:

1. **Twilio Account** with WhatsApp Sandbox enabled
2. **Google Cloud Project** with Drive API enabled
3. **OpenAI API Key** for document summarization
4. **n8n Instance** (self-hosted or cloud)

## Quick Setup

### 1. Twilio WhatsApp Sandbox Setup

1. Go to [Twilio Console](https://console.twilio.com/)
2. Navigate to Messaging → Try it out → WhatsApp Sandbox
3. Join the sandbox by sending the provided code to the WhatsApp number
4. Note your Account SID and Auth Token

### 2. Google Cloud Setup

1. Create a new project in [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the Google Drive API
3. Create OAuth 2.0 credentials:
   - Application type: Web application
   - Authorized redirect URIs: `https://your-n8n-instance.com/rest/oauth2-credential/callback`
4. Download the credentials JSON file

### 3. OpenAI API Setup

1. Get your API key from [OpenAI Platform](https://platform.openai.com/)
2. Ensure you have access to GPT-4 models

### 4. Environment Configuration

Copy the environment template and fill in your credentials:

```bash
cp .env.example .env
```

Edit `.env` with your actual credentials:

```env
# Twilio Configuration
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# Google Drive Configuration
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# n8n Configuration
N8N_HOST=your_n8n_host
N8N_PORT=5678
N8N_PROTOCOL=https
```

## Installation

### Option 1: Docker Deployment (Recommended)

1. Clone this repository:
```bash
git clone <repository-url>
cd WhatsApp-Driven-Google-Drive-Assistant
```

2. Start the services:
```bash
docker-compose up -d
```

3. Access n8n at `http://localhost:5678`

### Option 2: Manual n8n Setup

1. Install n8n:
```bash
npm install -g n8n
```

2. Start n8n:
```bash
n8n start
```

3. Import the workflow:
   - Open n8n web interface
   - Go to Workflows → Import from File
   - Select `workflows/whatsapp-drive-assistant.json`

## Workflow Configuration

### 1. Import the Workflow

1. Open your n8n instance
2. Click on "Import from File"
3. Select `workflows/whatsapp-drive-assistant.json`

### 2. Configure Credentials

Set up the following credentials in n8n:

#### Twilio Credentials
- Name: `Twilio WhatsApp`
- Account SID: Your Twilio Account SID
- Auth Token: Your Twilio Auth Token

#### Google Drive OAuth2
- Name: `Google Drive OAuth2`
- Client ID: Your Google OAuth2 Client ID
- Client Secret: Your Google OAuth2 Client Secret
- Scope: `https://www.googleapis.com/auth/drive`

#### OpenAI Credentials
- Name: `OpenAI API`
- API Key: Your OpenAI API Key

### 3. Configure Webhook URL

1. In the workflow, find the "Webhook" node
2. Copy the webhook URL
3. In Twilio Console, set this URL as your WhatsApp webhook endpoint

## Usage Examples

### List Files in a Folder
```
LIST /Projects/2024
```
Response:
```
📁 Files in /Projects/2024:
- project-proposal.pdf
- budget-analysis.xlsx
- meeting-notes.docx
```

### Move a File
```
MOVE /Projects/draft.pdf /Archive
```
Response:
```
✅ File 'draft.pdf' moved to /Archive
```

### Delete a File (with confirmation)
```
DELETE /temp/old-file.pdf
```
Response:
```
⚠️ Are you sure you want to delete 'old-file.pdf'? Reply with 'CONFIRM DELETE' to proceed.
```

### Get Document Summaries
```
SUMMARY /Reports/Q4
```
Response:
```
📄 Document Summaries for /Reports/Q4:

📋 quarterly-report.pdf:
• Revenue increased 15% compared to Q3
• New customer acquisition up 23%
• Operating expenses reduced by 8%

📋 market-analysis.docx:
• Competitive landscape analysis
• Market trends and opportunities
• Strategic recommendations for 2025
```

## Security Features

- **OAuth2 Authentication**: Secure Google Drive access
- **Confirmation Required**: Mass deletion operations require explicit confirmation
- **Audit Logging**: All operations are logged with timestamps and user information
- **Rate Limiting**: Built-in protection against spam commands
- **Scope Limitation**: Only operates within authenticated user's Google Drive

## Troubleshooting

### Common Issues

1. **Webhook not receiving messages**
   - Verify Twilio webhook URL is correctly set
   - Check n8n instance is publicly accessible
   - Ensure webhook path matches the configuration

2. **Google Drive authentication fails**
   - Verify OAuth2 credentials are correct
   - Check redirect URI matches n8n callback URL
   - Ensure Google Drive API is enabled

3. **AI summarization not working**
   - Verify OpenAI API key is valid
   - Check API quota and billing status
   - Ensure document format is supported (PDF, DOCX, TXT)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:
- Create an issue in this repository
- Check the troubleshooting section
- Review n8n documentation for workflow-specific issues
